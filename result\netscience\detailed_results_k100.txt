============================================================
离散Nelder-Mead算法详细结果报告
============================================================

【基本信息】
网络名称: netscience
种子集合大小: 100
单纯形维度: 30
传播概率: 0.05
最大迭代次数: 1
算法参数: α=0.5, γ=1.5, ρ=1.0, σ=0.375
PRE递推轮数: 5

【最终结果】
全局最优适应度: 127.730346
最优解发现代数: 0
IC模型估计影响力: 127.61
蒙特卡洛模拟次数: 1000
总运行时间: 2.71秒
最优种子集合: [2, 5, 8, 12, 14, 19, 21, 25, 26, 27, 32, 33, 35, 42, 44, 47, 50, 53, 54, 56, 62, 63, 64, 68, 71, 74, 77, 80, 84, 88, 92, 96, 106, 107, 112, 117, 125, 130, 135, 136, 139, 143, 144, 148, 151, 155, 157, 162, 163, 166, 167, 168, 169, 174, 177, 178, 179, 184, 185, 189, 196, 200, 202, 203, 208, 216, 218, 220, 222, 233, 242, 243, 247, 248, 258, 260, 268, 270, 275, 277, 280, 281, 283, 284, 286, 287, 292, 302, 304, 310, 317, 320, 338, 339, 340, 341, 343, 352, 358, 368]

【进化历史概览】
初始适应度: 126.328463
最终适应度: 126.328463
适应度提升: 0.000000
总改进次数: 0

【每代进化详情】
代数	当前最优	全局最优	平均值	标准差	是否更新
------------------------------------------------------------
0	126.3285	126.3285	123.7898	0.8685	否

============================================================
报告生成完成
