import numpy as np
from multiprocessing import Pool, cpu_count
from functools import partial


def _ic_single_run(iteration, seed, g, p):
    """
    单次IC模型传播模拟
    
    Args:
        iteration: 迭代次数（由multiprocessing传入，但在函数中不使用）
        seed: 种子节点集合
        g: NetworkX图对象
        p: 传播概率
        
    Returns:
        int: 受影响节点的总数
    """
    seed = set(seed)
    new_active, last_active = set(seed), set(seed)
    while new_active:
        new_ones = set()
        for node in new_active:
            node_neighbors = list(g.neighbors(node))
            for neighbor in node_neighbors:
                if neighbor not in last_active and np.random.uniform(0, 1) < p:
                    new_ones.add(neighbor)
        new_active = new_ones - last_active
        last_active.update(new_active)
    return len(last_active)

def IC(g, seed, p, mc=1000):
    """
    并行版本的IC模型影响力传播模拟
    
    Args:
        g: NetworkX图对象
        seed: 种子节点集合
        p: 传播概率
        mc: Monte Carlo模拟次数
    
    Returns:
        float: 平均影响力大小
    """
    # 确定要使用的CPU核心数
    # num_cores = min(cpu_count(), mc)
    num_cores = 16
    
    # 创建偏函数，固定g和p参数
    ic_partial = partial(_ic_single_run, seed=seed, g=g, p=p)
    
    # 使用进程池并行执行模拟
    with Pool(num_cores) as pool:
        results = pool.map(ic_partial, range(mc))
    
    # 返回平均影响力
    return np.mean(results)