============================================================
离散Nelder-Mead算法详细结果报告
============================================================

【基本信息】
网络名称: blog
种子集合大小: 100
单纯形维度: 30
传播概率: 0.05
最大迭代次数: 1
算法参数: α=0.5, γ=1.5, ρ=1.0, σ=0.375
PRE递推轮数: 5

【最终结果】
全局最优适应度: 229.007246
最优解发现代数: 0
IC模型估计影响力: 227.56
蒙特卡洛模拟次数: 1000
总运行时间: 91.17秒
最优种子集合: [3, 6, 10, 17, 20, 25, 33, 41, 64, 73, 76, 87, 101, 106, 108, 110, 119, 124, 126, 129, 141, 143, 152, 169, 173, 178, 197, 201, 223, 235, 236, 237, 238, 243, 249, 264, 326, 365, 373, 375, 388, 391, 416, 439, 444, 448, 476, 492, 497, 508, 538, 581, 610, 623, 661, 682, 703, 727, 758, 815, 820, 826, 888, 944, 1003, 1014, 1103, 1119, 1171, 1326, 1408, 1492, 1494, 1548, 1588, 1592, 1650, 1761, 1762, 1870, 2006, 2045, 2129, 2138, 2244, 2352, 2539, 2565, 2608, 2656, 2668, 2718, 2795, 3063, 3150, 3246, 3515, 3517, 3734, 3837]

【进化历史概览】
初始适应度: 220.426831
最终适应度: 220.426831
适应度提升: 0.000000
总改进次数: 0

【每代进化详情】
代数	当前最优	全局最优	平均值	标准差	是否更新
------------------------------------------------------------
0	220.4268	220.4268	201.6535	7.5948	否

============================================================
报告生成完成
