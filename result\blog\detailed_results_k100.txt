============================================================
离散Nelder-Mead算法详细结果报告
============================================================

【基本信息】
网络名称: blog
种子集合大小: 100
单纯形维度: 30
传播概率: 0.05
最大迭代次数: 200
算法参数: α=0.5, γ=1.5, ρ=1.0, σ=0.375
PRE递推轮数: 5

【最终结果】
全局最优适应度: 261.289714
最优解发现代数: 109
IC模型估计影响力: 260.74
蒙特卡洛模拟次数: 1000
总运行时间: 181.33秒
最优种子集合: [3, 6, 10, 20, 25, 33, 41, 44, 49, 54, 61, 64, 73, 76, 87, 91, 94, 98, 101, 106, 108, 110, 115, 119, 124, 126, 129, 141, 143, 152, 169, 173, 175, 178, 184, 193, 197, 201, 221, 223, 232, 235, 238, 243, 249, 307, 326, 365, 373, 375, 388, 391, 411, 416, 418, 430, 439, 444, 445, 448, 450, 492, 497, 508, 538, 554, 556, 581, 591, 610, 623, 632, 661, 682, 698, 727, 735, 758, 767, 815, 820, 826, 866, 888, 890, 920, 944, 1011, 1119, 1171, 1301, 1365, 1532, 1565, 1629, 1639, 1900, 2065, 2163, 2292]

【进化历史概览】
初始适应度: 217.071119
最终适应度: 260.573316
适应度提升: 43.502197
总改进次数: 36
主要改进节点:
  第1代: 243.382952
  第3代: 248.616484
  第5代: 251.634629
  第6代: 252.768142
  第8代: 253.004694
  第9代: 253.379103
  第11代: 253.643850
  第12代: 253.913624
  第14代: 254.324252
  第15代: 254.862152

【每代进化详情】
代数	当前最优	全局最优	平均值	标准差	是否更新
------------------------------------------------------------
0	217.0711	217.0711	201.8259	6.1934	否
1	243.3830	243.3830	237.3724	4.2393	否
2	242.6599	243.3830	234.5632	5.2599	否
3	243.9610	248.6165	235.8589	4.4612	否
4	242.8435	248.6165	235.4632	4.3587	否
5	244.2261	251.6346	235.6923	3.5038	否
6	246.9895	252.7681	236.2176	4.0506	否
7	242.2391	252.7681	236.8880	3.2025	否
8	244.9548	253.0047	238.0385	3.6477	否
9	253.3791	253.3791	239.2735	4.3872	否
10	245.4065	253.3791	238.5252	3.9408	否
11	246.8864	253.6439	239.9452	3.3606	否
12	253.9136	253.9136	240.2977	4.3164	否
13	248.8797	253.9136	240.6825	3.6658	否
14	254.3243	254.3243	241.6638	3.9445	否
15	254.8622	254.8622	241.5222	4.0693	否
16	255.2621	255.2621	240.2784	3.3072	否
17	255.2621	255.2621	240.9377	3.1329	否
18	246.0187	255.2621	241.2108	2.0075	否
19	245.7203	255.2621	241.0704	2.2150	否
20	255.2621	255.2621	241.9155	3.0675	否
21	255.2621	255.2621	242.8704	2.9782	否
22	256.2508	256.2508	243.1210	3.3406	否
23	256.3751	256.3751	243.3653	3.3114	否
24	256.3854	256.3854	244.2398	3.1781	否
25	257.4654	257.4654	245.0478	3.4428	否
26	257.4654	257.4654	245.6507	3.2321	否
27	257.4654	257.4654	246.1580	3.3026	否
28	257.4654	257.4654	246.3374	3.5373	否
29	257.4654	257.4654	246.7741	3.5995	否
30	257.4654	257.4654	247.3035	3.5296	否
31	257.9915	257.9915	248.0827	3.4726	否
32	257.9915	257.9915	248.4766	3.2574	否
33	257.9915	257.9915	248.9623	3.1712	否
34	257.9915	257.9915	249.3905	3.2011	否
35	257.9915	257.9915	249.7001	2.9777	否
36	257.9915	257.9915	250.3382	2.8429	否
37	257.9915	257.9915	250.9278	2.7565	否
38	257.9915	257.9915	251.5479	2.6353	否
39	257.9915	257.9915	252.1423	2.5601	否
40	258.4183	258.4183	252.6815	2.4355	否
41	258.4183	258.4183	253.1241	2.4020	否
42	258.4183	258.4183	253.5714	2.2236	否
43	258.4183	258.4183	254.0043	2.0649	否
44	258.4183	258.4183	254.4699	1.8883	否
45	258.4183	258.4183	254.8334	1.7944	否
46	258.4183	258.4183	255.1712	1.5912	否
47	258.4183	258.4183	255.6478	1.3389	否
48	258.4183	258.4183	255.9383	1.1421	否
49	258.4183	258.4183	256.1273	1.0411	否
50	258.4183	258.4183	256.2655	0.9276	否
51	258.4183	258.4183	256.4105	0.8720	否
52	258.4183	258.4183	256.4919	0.7852	否
53	258.4183	258.4183	256.5581	0.7523	否
54	258.4183	258.4183	256.6431	0.7017	否
55	258.4183	258.4183	256.6652	0.7026	否
56	258.4183	258.4183	256.6930	0.6888	否
57	258.4183	258.4183	256.7154	0.6865	否
58	258.4183	258.4183	256.7317	0.6580	否
59	258.4183	258.4183	256.7377	0.6443	否
60	258.4183	258.4183	256.7691	0.6291	否
61	258.4183	258.4183	256.8108	0.5639	否
62	258.4183	258.4183	256.8811	0.5429	否
63	258.4183	258.4183	256.9040	0.5071	否
64	258.4183	258.4183	256.9186	0.4953	否
65	258.4183	258.4183	256.9412	0.4136	否
66	258.4183	258.4183	256.9669	0.3819	否
67	258.4183	258.4183	257.0028	0.3683	否
68	258.4183	258.4183	258.4183	0.0000	否
69	258.9009	258.9009	258.4630	0.0974	否
70	259.1390	259.1390	258.5485	0.1787	否
71	259.1390	259.1390	258.6507	0.2069	否
72	259.2905	259.2905	258.7135	0.2302	否
73	259.3329	259.3329	258.7636	0.2462	否
74	259.3329	259.3329	258.7897	0.2355	否
75	259.5286	259.5286	258.9489	0.2318	否
76	259.5751	259.5751	259.1172	0.1977	否
77	259.7744	259.7744	259.2184	0.2075	否
78	259.7744	259.7744	259.2720	0.2174	否
79	259.8533	259.8533	259.3143	0.2265	否
80	259.8533	259.8533	259.3339	0.2232	否
81	259.9414	259.9414	259.5685	0.2093	否
82	259.9414	259.9414	259.6596	0.1544	否
83	260.0308	260.0308	259.7019	0.1565	否
84	260.0308	260.0308	259.7095	0.1485	否
85	260.0308	260.0308	259.7236	0.1291	否
86	260.0708	260.0708	259.7464	0.1356	否
87	260.0708	260.0708	259.7477	0.1369	否
88	260.0708	260.0708	259.7500	0.1361	否
89	260.0708	260.0708	259.7501	0.1360	否
90	260.0708	260.0708	259.7512	0.1362	否
91	260.0708	260.0708	259.7546	0.1341	否
92	260.0708	260.0708	259.7620	0.1421	否
93	260.0708	260.0708	259.7633	0.1414	否
94	260.0708	260.0708	259.7692	0.1364	否
95	260.1428	260.1428	259.7927	0.1439	否
96	260.2038	260.2038	259.8159	0.1536	否
97	260.2921	260.2921	259.8651	0.1791	否
98	260.2921	260.2921	259.8795	0.1846	否
99	260.2938	260.2938	259.8996	0.1870	否
100	260.5227	260.5227	259.9160	0.2171	否
101	260.5227	260.5227	260.5227	0.0000	否
102	260.5531	260.5531	260.5237	0.0054	否
103	260.5531	260.5531	260.5531	0.0000	否
104	260.5531	260.5531	260.5531	0.0000	否
105	260.5531	260.5531	260.5531	0.0000	否
106	260.5531	260.5531	260.5531	0.0000	否
107	260.5531	260.5531	260.5531	0.0000	否
108	260.5660	260.5660	260.5536	0.0023	否
109	260.5733	260.5733	260.5549	0.0054	否
110	260.5733	260.5733	260.5733	0.0000	否
111	260.5733	260.5733	260.5733	0.0000	否
112	260.5733	260.5733	260.5733	0.0000	否
113	260.5733	260.5733	260.5733	0.0000	否
114	260.5733	260.5733	260.5733	0.0000	否
115	260.5733	260.5733	260.5733	0.0000	否
116	260.5733	260.5733	260.5733	0.0000	否
117	260.5733	260.5733	260.5733	0.0000	否
118	260.5733	260.5733	260.5733	0.0000	否
119	260.5733	260.5733	260.5733	0.0000	否
120	260.5733	260.5733	260.5733	0.0000	否
121	260.5733	260.5733	260.5733	0.0000	否
122	260.5733	260.5733	260.5733	0.0000	否
123	260.5733	260.5733	260.5733	0.0000	否
124	260.5733	260.5733	260.5733	0.0000	否
125	260.5733	260.5733	260.5733	0.0000	否
126	260.5733	260.5733	260.5733	0.0000	否
127	260.5733	260.5733	260.5733	0.0000	否
128	260.5733	260.5733	260.5733	0.0000	否
129	260.5733	260.5733	260.5733	0.0000	否
130	260.5733	260.5733	260.5733	0.0000	否
131	260.5733	260.5733	260.5733	0.0000	否
132	260.5733	260.5733	260.5733	0.0000	否
133	260.5733	260.5733	260.5733	0.0000	否
134	260.5733	260.5733	260.5733	0.0000	否
135	260.5733	260.5733	260.5733	0.0000	否
136	260.5733	260.5733	260.5733	0.0000	否
137	260.5733	260.5733	260.5733	0.0000	否
138	260.5733	260.5733	260.5733	0.0000	否
139	260.5733	260.5733	260.5733	0.0000	否
140	260.5733	260.5733	260.5733	0.0000	否
141	260.5733	260.5733	260.5733	0.0000	否
142	260.5733	260.5733	260.5733	0.0000	否
143	260.5733	260.5733	260.5733	0.0000	否
144	260.5733	260.5733	260.5733	0.0000	否
145	260.5733	260.5733	260.5733	0.0000	否
146	260.5733	260.5733	260.5733	0.0000	否
147	260.5733	260.5733	260.5733	0.0000	否
148	260.5733	260.5733	260.5733	0.0000	否
149	260.5733	260.5733	260.5733	0.0000	否
150	260.5733	260.5733	260.5733	0.0000	否
151	260.5733	260.5733	260.5733	0.0000	否
152	260.5733	260.5733	260.5733	0.0000	否
153	260.5733	260.5733	260.5733	0.0000	否
154	260.5733	260.5733	260.5733	0.0000	否
155	260.5733	260.5733	260.5733	0.0000	否
156	260.5733	260.5733	260.5733	0.0000	否
157	260.5733	260.5733	260.5733	0.0000	否
158	260.5733	260.5733	260.5733	0.0000	否
159	260.5733	260.5733	260.5733	0.0000	否
160	260.5733	260.5733	260.5733	0.0000	否
161	260.5733	260.5733	260.5733	0.0000	否
162	260.5733	260.5733	260.5733	0.0000	否
163	260.5733	260.5733	260.5733	0.0000	否
164	260.5733	260.5733	260.5733	0.0000	否
165	260.5733	260.5733	260.5733	0.0000	否
166	260.5733	260.5733	260.5733	0.0000	否
167	260.5733	260.5733	260.5733	0.0000	否
168	260.5733	260.5733	260.5733	0.0000	否
169	260.5733	260.5733	260.5733	0.0000	否
170	260.5733	260.5733	260.5733	0.0000	否
171	260.5733	260.5733	260.5733	0.0000	否
172	260.5733	260.5733	260.5733	0.0000	否
173	260.5733	260.5733	260.5733	0.0000	否
174	260.5733	260.5733	260.5733	0.0000	否
175	260.5733	260.5733	260.5733	0.0000	否
176	260.5733	260.5733	260.5733	0.0000	否
177	260.5733	260.5733	260.5733	0.0000	否
178	260.5733	260.5733	260.5733	0.0000	否
179	260.5733	260.5733	260.5733	0.0000	否
180	260.5733	260.5733	260.5733	0.0000	否
181	260.5733	260.5733	260.5733	0.0000	否
182	260.5733	260.5733	260.5733	0.0000	否
183	260.5733	260.5733	260.5733	0.0000	否
184	260.5733	260.5733	260.5733	0.0000	否
185	260.5733	260.5733	260.5733	0.0000	否
186	260.5733	260.5733	260.5733	0.0000	否
187	260.5733	260.5733	260.5733	0.0000	否
188	260.5733	260.5733	260.5733	0.0000	否
189	260.5733	260.5733	260.5733	0.0000	否
190	260.5733	260.5733	260.5733	0.0000	否
191	260.5733	260.5733	260.5733	0.0000	否
192	260.5733	260.5733	260.5733	0.0000	否
193	260.5733	260.5733	260.5733	0.0000	否
194	260.5733	260.5733	260.5733	0.0000	否
195	260.5733	260.5733	260.5733	0.0000	否
196	260.5733	260.5733	260.5733	0.0000	否
197	260.5733	260.5733	260.5733	0.0000	否
198	260.5733	260.5733	260.5733	0.0000	否
199	260.5733	260.5733	260.5733	0.0000	否

============================================================
报告生成完成
