#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
局部搜索缓存机制测试脚本
验证缓存机制是否正确工作，确保计算逻辑不变
"""

import time
import numpy as np
import networkx as nx
from typing import Set, Dict, List

# 导入模块
from base_fun import gen_graph, local_search, PRE

def test_local_search_cache():
    """测试局部搜索缓存机制"""
    print("=" * 60)
    print("局部搜索缓存机制测试")
    print("=" * 60)
    
    # 测试参数
    network_path = "D:\\VS\\code\\networks\\NetHEHT.txt"
    
    # 算法参数
    k = 20        # 种子集合大小
    p = 0.05      # 传播概率
    max_hop = 3   # PRE递推轮数
    
    print(f"测试参数: k={k}, p={p}, max_hop={max_hop}")
    print()
    
    # 加载网络
    print("正在加载网络...")
    try:
        g = gen_graph(network_path)
        print(f"网络规模: {g.number_of_nodes()}个节点, {g.number_of_edges()}条边")
    except Exception as e:
        print(f"网络加载失败: {e}")
        print("使用随机生成的小网络进行测试...")
        g = nx.erdos_renyi_graph(100, 0.1)
        print(f"生成随机网络: {g.number_of_nodes()}个节点, {g.number_of_edges()}条边")
    print()
    
    # 预计算邻接列表
    if not hasattr(g, '_neighbors_cache'):
        g._neighbors_cache = {v: list(g.neighbors(v)) for v in g.nodes()}
    neighbors = g._neighbors_cache
    
    # 生成初始种子集合（选择度最高的k个节点）
    nodes_by_degree = sorted(g.nodes(), key=lambda x: g.degree(x), reverse=True)
    initial_seed = set(nodes_by_degree[:k])
    
    print(f"初始种子集合: {sorted(list(initial_seed))}")
    print()
    
    # 测试1: 不使用缓存的局部搜索
    print("=== 测试1: 不使用缓存的局部搜索 ===")
    start_time = time.time()
    result_no_cache = local_search(initial_seed, g, p, k, neighbors, max_hop, fitness_cache=None)
    time_no_cache = time.time() - start_time
    print(f"不使用缓存耗时: {time_no_cache:.4f}秒")
    print(f"优化后种子集合: {sorted(result_no_cache)}")
    
    # 计算优化后的适应度
    fitness_no_cache = PRE(g, set(result_no_cache), p, neighbors, max_hop)
    print(f"优化后适应度: {fitness_no_cache:.6f}")
    print()
    
    # 测试2: 使用缓存的局部搜索
    print("=== 测试2: 使用缓存的局部搜索 ===")
    cache = {}
    start_time = time.time()
    result_with_cache = local_search(initial_seed, g, p, k, neighbors, max_hop, fitness_cache=cache)
    time_with_cache = time.time() - start_time
    print(f"使用缓存耗时: {time_with_cache:.4f}秒")
    print(f"优化后种子集合: {sorted(result_with_cache)}")
    print(f"缓存大小: {len(cache)} 个不同的种子集合")
    
    # 计算优化后的适应度
    fitness_with_cache = PRE(g, set(result_with_cache), p, neighbors, max_hop)
    print(f"优化后适应度: {fitness_with_cache:.6f}")
    print()
    
    # 验证结果一致性
    print("=== 结果一致性验证 ===")
    results_match = (set(result_no_cache) == set(result_with_cache))
    fitness_match = abs(fitness_no_cache - fitness_with_cache) < 1e-10
    
    print(f"种子集合一致性: {'✓ 通过' if results_match else '✗ 失败'}")
    print(f"适应度一致性: {'✓ 通过' if fitness_match else '✗ 失败'}")
    
    if results_match and fitness_match:
        print("✓ 缓存机制正确工作，计算逻辑完全不变")
    else:
        print("✗ 缓存机制存在问题，需要检查实现")
        print(f"无缓存结果: {sorted(result_no_cache)}")
        print(f"有缓存结果: {sorted(result_with_cache)}")
        print(f"无缓存适应度: {fitness_no_cache:.10f}")
        print(f"有缓存适应度: {fitness_with_cache:.10f}")
    print()
    
    # 性能分析
    print("=== 性能分析 ===")
    if time_with_cache > 0:
        speedup = time_no_cache / time_with_cache
        improvement = (time_no_cache - time_with_cache) / time_no_cache * 100
        print(f"加速比: {speedup:.2f}x")
        print(f"性能提升: {improvement:.1f}%")
        print(f"时间节省: {time_no_cache - time_with_cache:.4f}秒")
    else:
        print("缓存版本执行时间过短，无法准确测量性能提升")
    
    print(f"缓存命中潜力: 在局部搜索过程中缓存了 {len(cache)} 个不同的种子集合")
    print("缓存机制在重复计算相同种子集合时能显著提升性能")
    print()
    
    # 测试3: 多次运行验证缓存效果
    print("=== 测试3: 多次运行验证缓存效果 ===")
    cache_multi = {}
    total_time = 0
    num_runs = 3
    
    for i in range(num_runs):
        start_time = time.time()
        result = local_search(initial_seed, g, p, k, neighbors, max_hop, fitness_cache=cache_multi)
        run_time = time.time() - start_time
        total_time += run_time
        print(f"第{i+1}次运行耗时: {run_time:.4f}秒, 缓存大小: {len(cache_multi)}")
    
    avg_time = total_time / num_runs
    print(f"平均耗时: {avg_time:.4f}秒")
    print(f"最终缓存大小: {len(cache_multi)} 个不同的种子集合")
    print("注意: 后续运行应该更快，因为可以重用之前的缓存结果")

if __name__ == "__main__":
    test_local_search_cache()
