# ==============================================================================
# 离散 Nelder-Mead 算法实现 - 影响力最大化问题 
# ==============================================================================

import numpy as np
import networkx as nx
import random
import time
import os
from typing import List, Set, Tuple, Optional, Dict
from functools import lru_cache

# 自定义模块导入
from base_fun import gen_graph, PRE
from local_search import local_search, local_search_iterative, local_search_best_improvement
from NM_fun import (
    objective, evaluate_simplex, centroid_discrete,
    reflect_step, expand_step, contract_outside_step, contract_inside_step,
    degree_initialization, shrink_step, get_operator_statistics, reset_operator_statistics
)
from plot import plot_evolution_results, create_summary_plot
from save import _save_detailed_results_txt
from IC import IC
# ==============================================================================
# 辅助函数
# ==============================================================================

def _summarize_set(s: Set[int]) -> str:
    """集合输出摘要：输出所有节点"""
    arr = sorted(s)
    return f"{arr}"


# ==============================================================================
# 主算法：离散 Nelder-Mead 算法
# ==============================================================================

def NM(G: nx.Graph, n: int, k: int, p: float, gmax: int,
       alpha: float = 1, gamma: float = 2, rho: float = 1, sigma: float = 0.5,
       max_hop: int = 5, random_seed: Optional[int] = None,
       verbose: bool = True, network_name: str = "unknown") -> Tuple[Set[int], List[float], Dict]:
    """
    离散 Nelder–Mead（DM）算法：选择规模为 k 的种子集合，以最大化 PRE 估计的传播 (优化版本)

    Args:
        G: 网络图
        n: 单纯形"维度"（单纯形规模为 n+1）
        k: 种子集合大小
        p: 传播概率
        gmax: 最大迭代次数
        alpha: 反射系数
        gamma: 扩展系数
        rho: 收缩系数
        sigma: 收缩回退比例
        max_hop: PRE 递推轮数
        random_seed: 随机种子
        verbose: 是否输出详细信息
        network_name: 网络名称，用于结果文件命名

    Returns:
        (最优解集合, 每轮迭代的最优适应度序列, 详细结果信息)
    """

    # 重置算子统计信息
    reset_operator_statistics()

    # 预计算邻接列表，供 PRE 使用（优化：避免重复转换）
    if not hasattr(G, '_neighbors_cache'):
        G._neighbors_cache = {v: list(G.neighbors(v)) for v in G.nodes()}
    neighbors: Dict[int, List[int]] = G._neighbors_cache

    # 优化1: 预计算节点列表和度信息，避免重复计算
    if not hasattr(G, '_node_list_cache'):
        G._node_list_cache = list(G.nodes())
    if not hasattr(G, '_degree_cache'):
        G._degree_cache = {v: G.degree(v) for v in G.nodes()}

    # 优化2: 创建适应度缓存，避免重复计算相同种子集合的适应度
    fitness_cache = {}

    def cached_objective(seed_set: Set[int]) -> float:
        """带缓存的目标函数计算"""
        # 将集合转换为排序的元组作为缓存键
        cache_key = tuple(sorted(seed_set))
        if cache_key not in fitness_cache:
            fitness_cache[cache_key] = objective(G, seed_set, p, neighbors, max_hop)
        return fitness_cache[cache_key]

    # 初始化单纯形（n+1 个解，每个为大小为 k 的种子集合）
    simplex = degree_initialization(G, n, k, random_seed=random_seed)
    # 优化3: 使用缓存的目标函数进行评估
    scored_simplex = [(cached_objective(s), s) for s in simplex]
    scored_simplex.sort(key=lambda t: t[0], reverse=True)

    if verbose:
        print("初始化单纯形完成：")
        init_scores = [float(f) for f, _ in scored_simplex]
        print(f"- 初始适应度范围：min={min(init_scores):.4f} max={max(init_scores):.4f} "
              f"均值={np.mean(init_scores):.4f} std={np.std(init_scores):.6f}")
        print(f"- 初始解数量：{len(scored_simplex)}，每个解大小k={k}")

    best_history: List[float] = []

    # 全局最优个体跟踪
    global_best_fitness = float('-inf')
    global_best_solution = None
    global_best_generation = -1

    # 创建结果保存目录（按网络名称分文件夹）
    result_dir = f"result/{network_name}"
    os.makedirs(result_dir, exist_ok=True)

    # 详细结果记录
    detailed_results = {
        'network_name': network_name,
        'parameters': {
            'n': n, 'k': k, 'p': p, 'gmax': gmax,
            'alpha': alpha, 'gamma': gamma, 'rho': rho, 'sigma': sigma, 'max_hop': max_hop
        },
        'generations': [],
        'global_best_history': [],
        'final_best_fitness': None,
        'final_best_solution': None,
        'total_runtime': None
    }

    # 获取算子统计实例
    stats = get_operator_statistics()

    # 主循环：最多 gmax 轮（每轮对所有个体尝试进化）
    for it in range(gmax):
        # 每代局部搜索标志，确保每代最多进行一次局部搜索
        local_search_performed_this_generation = False
        # 按适应度降序排列
        scored_simplex.sort(key=lambda t: t[0], reverse=True)
        f_best, x_best = scored_simplex[0]
        f_vals = np.array([f for f, _ in scored_simplex], dtype=float)

        # 更新全局最优个体（确保适应度不会变差）
        if f_best > global_best_fitness:
            global_best_fitness = f_best
            global_best_solution = x_best.copy() if hasattr(x_best, 'copy') else set(x_best)
            global_best_generation = it
            if verbose:
                print(f"*** 发现新的全局最优解！代数={it}, 适应度={f_best:.4f} ***")

        # 精英保留策略：确保全局最优解始终在单纯形中
        # 检查当前单纯形是否包含全局最优解
        global_best_in_simplex = False
        for _, x_i in scored_simplex:
            if x_i == global_best_solution:
                global_best_in_simplex = True
                break

        # 如果全局最优解不在当前单纯形中，替换最差解
        if not global_best_in_simplex:
            scored_simplex[-1] = (global_best_fitness, global_best_solution)
            scored_simplex.sort(key=lambda t: t[0], reverse=True)
            if verbose:
                print(f"*** 精英保留：将全局最优解重新加入单纯形 ***")

        # 记录全局最优适应度（确保单调递增）
        best_history.append(float(global_best_fitness))

        # 记录详细的代信息
        generation_info = {
            'generation': it,
            'current_best_fitness': float(f_best),
            'global_best_fitness': float(global_best_fitness),
            'fitness_stats': {
                'min': float(f_vals.min()),
                'max': float(f_vals.max()),
                'mean': float(f_vals.mean()),
                'std': float(f_vals.std())
            },
            'is_global_best_updated': f_best > global_best_fitness
        }
        detailed_results['generations'].append(generation_info)
        detailed_results['global_best_history'].append(float(global_best_fitness))

        if verbose:
            f_worst = scored_simplex[-1][0]
            print(f"\n【迭代 {it}】全局最优值={global_best_fitness:.4f} 当前单纯形最优值={f_best:.4f} 最差值={f_worst:.4f} "
                  f"标准差={f_vals.std():.6f}")

            # 显示前3和后3的适应度
            head = ", ".join([f"{scored_simplex[i][0]:.4f}"
                             for i in range(min(3, len(scored_simplex)))])
            tail = ", ".join([f"{scored_simplex[-i-1][0]:.4f}"
                             for i in range(min(3, len(scored_simplex)))][::-1])
            print(f"- 前3适应度：[{head}]  后3适应度：[{tail}]")
            print(f"- 全局最优解：{_summarize_set(set(global_best_solution))}")
            if f_best != global_best_fitness:
                print(f"- 当前单纯形最优解：{_summarize_set(set(x_best))}")

        # 基于当前单纯形，对每个个体独立计算（排除自身）质心并完成一轮进化
        current_scored = list(scored_simplex)
        new_candidates: List[Tuple[float, Set[int]]] = []
        improved_count = 0

        # 预计算所有个体的集合，避免重复转换
        all_sets = [s for _, s in current_scored]

        # 优化4: 预计算所有个体的适应度阈值，避免重复计算
        others_scores_cache = {}
        for j in range(len(current_scored)):
            if j == 0:
                others_scores_cache[j] = [current_scored[i][0] for i in range(1, len(current_scored))]
            elif j == len(current_scored) - 1:
                others_scores_cache[j] = [current_scored[i][0] for i in range(len(current_scored) - 1)]
            else:
                others_scores_cache[j] = [current_scored[i][0] for i in range(len(current_scored)) if i != j]

        # 记录收缩失败的个体数量
        contract_failed_count = 0

        # 对每个个体进行进化操作
        for j, (f_j, x_j) in enumerate(current_scored):
            # 计算排除自身后的离散质心
            x_c = centroid_discrete(G, all_sets, exclude_set=x_j, k=k)
            D = x_c - x_j

            # 若 D 为空或质心与自身重合，跳过该个体的进化操作
            if len(D) == 0:
                candidate = (f_j, x_j)  # 保持原个体不变
            else:
                # 反射操作
                x_r = reflect_step(G, x_c, x_j, alpha=alpha, k=k, verbose=False, stats=stats)
                f_r = cached_objective(x_r)  # 优化5: 使用缓存的目标函数

                # 针对个体 j 的阈值（其余个体中的最差）- 优化6: 使用预计算的阈值
                others_scores = others_scores_cache[j]
                f_threshold = min(others_scores) if others_scores else f_j

                # 根据反射结果决定下一步操作
                candidate: Tuple[float, Set[int]]

                if f_r > f_best:
                    # 反射成功（超过最优解）
                    stats.record_reflect(True)

                    # 扩展操作：计算从质心到反射点的方向向量
                    expand_direction = x_r - x_c  # 扩展方向：从质心指向反射点
                    x_e = expand_step(G, x_r, expand_direction, gamma=gamma, k=k, verbose=False, stats=stats)
                    f_e = cached_objective(x_e)  # 优化7: 使用缓存的目标函数
                    if f_e > f_r:
                        # 扩展成功
                        stats.record_expand(True)
                        candidate = (f_e, x_e)

                        # 扩展成功后对全局最优解进行局部搜索（每代最多一次）
                        if not local_search_performed_this_generation:
                            if verbose:
                                print(f"*** 扩展成功！对全局最优解进行局部搜索优化 ***")

                            # 获取当前全局最优解
                            current_global_best = global_best_solution
                            if current_global_best is not None:
                                # 计算局部搜索前的适应度
                                original_fitness = cached_objective(current_global_best)  # 优化8: 使用缓存

                                # 执行局部搜索（传递缓存以避免重复计算）
                                optimized_solution = local_search(
                                    current_global_best, G, p, k, neighbors, max_hop, fitness_cache,
                                    n_jobs=4  # 使用4个线程进行并行计算
                                )
                                optimized_solution_set = set(optimized_solution)

                                # 计算局部搜索后的适应度
                                optimized_fitness = cached_objective(optimized_solution_set)  # 优化9: 使用缓存

                                # 如果局部搜索找到了更好的解，更新全局最优解
                                if optimized_fitness > original_fitness:
                                    global_best_fitness = optimized_fitness
                                    global_best_solution = optimized_solution_set
                                    global_best_generation = it
                                    if verbose:
                                        improvement = optimized_fitness - original_fitness
                                        print(f"*** 局部搜索成功！适应度从 {original_fitness:.4f} 提升到 {optimized_fitness:.4f} (提升: {improvement:.4f}) ***")
                                elif verbose:
                                    print(f"局部搜索未找到更好解，保持原解 (适应度: {original_fitness:.4f})")

                                # 标记本代已进行局部搜索
                                local_search_performed_this_generation = True
                        # elif verbose:
                        #     print(f"*** 扩展成功，但本代已进行过局部搜索，跳过 ***")
                    else:
                        # 扩展失败
                        stats.record_expand(False)
                        candidate = (f_r, x_r)
                elif f_r > f_threshold:
                    # 反射成功（超过阈值）
                    stats.record_reflect(True)
                    # 接受反射结果
                    candidate = (f_r, x_r)
                else:
                    # 反射失败
                    stats.record_reflect(False)

                    # 收缩操作
                    contract_success = False
                    if f_r > f_j:
                        # 外部收缩
                        x_co = contract_outside_step(G, x_c, x_j, rho=rho, k=k, verbose=False, stats=stats)
                        f_co = cached_objective(x_co)  # 优化10: 使用缓存的目标函数
                        if f_co > f_j:
                            # 外部收缩成功
                            stats.record_contract_outside(True)
                            candidate = (f_co, x_co)
                            contract_success = True
                        else:
                            # 外部收缩失败
                            stats.record_contract_outside(False)
                            candidate = (f_j, x_j)
                    else:
                        # 内部收缩
                        x_ci = contract_inside_step(G, x_j, x_c, rho=rho, k=k, verbose=False, stats=stats)
                        f_ci = cached_objective(x_ci)  # 优化11: 使用缓存的目标函数
                        if f_ci > f_j:
                            # 内部收缩成功
                            stats.record_contract_inside(True)
                            candidate = (f_ci, x_ci)
                            contract_success = True
                        else:
                            # 内部收缩失败
                            stats.record_contract_inside(False)
                            candidate = (f_j, x_j)

                    # 记录收缩失败的个体
                    if not contract_success:
                        contract_failed_count += 1

            # 统计改进个体数量
            if candidate[0] > f_j:
                improved_count += 1

            new_candidates.append(candidate)

        # 计算需要收缩的个体总数（即D不为空的个体数）
        need_contract_count = 0
        for j, (f_j, x_j) in enumerate(current_scored):
            x_c = centroid_discrete(G, all_sets, exclude_set=x_j, k=k)
            D = x_c - x_j
            if len(D) > 0:
                need_contract_count += 1

        # 检测收缩失败并执行回退操作
        # 若所有需要收缩的操作均失败，执行收缩回退
        if need_contract_count > 0 and contract_failed_count == need_contract_count:
            if verbose:
                print(f"*** 检测到收缩均失败（失败数：{contract_failed_count}/{need_contract_count}），执行收缩回退操作 ***")
            # 使用shrink_step进行收缩回退
            new_simplex_sets = shrink_step(G, current_scored, k, sigma, verbose=verbose, stats=stats)
            # 优化12: 使用缓存的目标函数评估新单纯形
            scored_simplex = [(cached_objective(s), s) for s in new_simplex_sets]
            scored_simplex.sort(key=lambda t: t[0], reverse=True)
        else:
            # 统一评估整个人口
            if verbose:
                print(f"- 本轮改进个体数：{improved_count}/{len(current_scored)}")
                if contract_failed_count > 0:
                    print(f"- 收缩失败个体数：{contract_failed_count}/{need_contract_count}")
            # 优化13: 使用缓存的目标函数评估候选解
            scored_simplex = [(cached_objective(s), s) for s in [s for _, s in new_candidates]]
            scored_simplex.sort(key=lambda t: t[0], reverse=True)

        if verbose:
            f_vals_new = [f for f, _ in scored_simplex]
            f_vals_array = np.array(f_vals_new)  # 避免重复转换
            print(f"- 轮末适应度范围：min={f_vals_array.min():.4f} max={f_vals_array.max():.4f} "
                  f"均值={f_vals_array.mean():.4f} std={f_vals_array.std():.6f}")
            f_best_new, x_best_new = max(scored_simplex, key=lambda t: t[0])
            print(f"- 更新后新最优值={f_best_new:.4f}，最优解：{_summarize_set(x_best_new)}")

    # 达到迭代上限，使用全局最优解作为最终结果
    if verbose:
        print("达到最大迭代次数，返回全局最优解。")
        print(f"全局最优适应度：{global_best_fitness:.4f} (发现于第{global_best_generation}代)")
        print(f"全局最优解：{_summarize_set(global_best_solution)}")

        # 优化14: 输出缓存命中率统计
        cache_size = len(fitness_cache)
        print(f"适应度缓存大小: {cache_size} 个不同的种子集合")

        # 输出算子统计信息
        stats.print_statistics()

    # 确保返回的是set类型
    if not isinstance(global_best_solution, set):
        global_best_solution = set(global_best_solution)

    # 完善详细结果记录
    detailed_results['final_best_fitness'] = float(global_best_fitness)
    detailed_results['final_best_solution'] = sorted(list(global_best_solution))
    detailed_results['global_best_generation'] = global_best_generation

    # 添加算子统计信息到详细结果
    detailed_results['operator_statistics'] = {
        'summary': stats.get_summary(),
        'success_rates': stats.get_success_rates()
    }

    # 保存最优解到单独文件
    best_solution_file = os.path.join(result_dir, f"best_solution_k{k}.txt")
    with open(best_solution_file, 'w', encoding='utf-8') as f:
        f.write(f"网络: {network_name}\n")
        f.write(f"种子集合大小: {k}\n")
        f.write(f"最优适应度: {global_best_fitness:.6f}\n")
        f.write(f"发现代数: {global_best_generation}\n")
        f.write(f"最优种子集合: {sorted(list(global_best_solution))}\n")

        # 添加算子统计信息
        f.write(f"\n=== 算子统计信息 ===\n")
        summary = stats.get_summary()
        rates = stats.get_success_rates()
        for op_name, op_stats in summary.items():
            success = op_stats['success']
            attempts = op_stats['attempts']
            rate = rates.get(op_name, 0.0)
            f.write(f"{op_name:15}: {success:4d}/{attempts:4d} ({rate:6.2%})\n")

        total_success = sum(op_stats['success'] for op_stats in summary.values())
        total_attempts = sum(op_stats['attempts'] for op_stats in summary.values())
        overall_rate = total_success / total_attempts if total_attempts > 0 else 0.0
        f.write(f"{'总计':15}: {total_success:4d}/{total_attempts:4d} ({overall_rate:6.2%})\n")

    if verbose:
        print(f"基本结果已保存到: {result_dir}")

    return global_best_solution, best_history, detailed_results

# ==============================================================================
# 主函数
# ==============================================================================

def main():
    """主函数：运行离散NM算法求解影响力最大化问题"""
    start_time = time.time()

    # 网络数据路径配置
    network_path = "D:\\VS\\code\\networks\\netscience.txt"
    # network_path = "D:\\VS\\code\\networks\\blog.txt"
    network_path = "D:\\VS\\code\\networks\\pgp.txt"

    # network_path = "D:\\VS\\code\\networks\\CA-HepTh.txt"
    # network_path = "D:\\VS\\code\\networks\\NetHEHT.txt"
    # network_path = "D:\\VS\\code\\networks\\deezer.txt"

    # 提取网络名称
    network_name = os.path.splitext(os.path.basename(network_path))[0]

    # 加载网络图
    g = gen_graph(network_path)
    p = 0.05  # 传播概率

    # 算法参数配置
    n = 30        # 单纯形“维度”（单纯形规模为 n+1）
    k = 100        # 种子集合大小
    gmax = 1    # 最大迭代次数

    # alpha = 1.5     # 反射系数
    # gamma = 1     # 扩展系数
    # rho = 1.5       # 收缩系数
    # sigma = 0.25   # 收缩回退比例
    alpha = 0.5     # 反射系数
    gamma = 1.5     # 扩展系数
    rho = 1.0       # 收缩系数
    sigma = 0.375   # 收缩回退比例

    max_hop = 5   # PRE 递推轮数

    print(f"开始运行离散NM算法 - 网络: {network_name}, k={k}")
    print(f"网络规模: {g.number_of_nodes()}个节点, {g.number_of_edges()}条边")

    # 运行离散NM算法求解影响力最大化的种子集合
    seed, best_history, detailed_results = NM(
        g, n, k, p, gmax,
        alpha=alpha, gamma=gamma, rho=rho, sigma=sigma,
        max_hop=max_hop, verbose=True, network_name=network_name
    )

    # 对全局最优解进行局部搜索优化
    print(f"\n=== 开始局部搜索优化 ===")

    # 预计算邻接列表，供局部搜索使用
    if not hasattr(g, '_neighbors_cache'):
        g._neighbors_cache = {v: list(g.neighbors(v)) for v in g.nodes()}
    neighbors = g._neighbors_cache

    # 创建局部搜索专用的缓存
    local_search_cache = {}

    def cached_PRE_for_local_search(seed_set):
        """局部搜索专用的带缓存PRE计算函数"""
        cache_key = tuple(sorted(seed_set))
        if cache_key not in local_search_cache:
            local_search_cache[cache_key] = PRE(g, seed_set, p, neighbors, max_hop)
        return local_search_cache[cache_key]

    # 计算局部搜索前的PRE适应度
    original_fitness = cached_PRE_for_local_search(seed)
    print(f"局部搜索前PRE适应度: {original_fitness:.6f}")
    print(f"局部搜索前种子集合: {sorted(seed)}")

    # 执行局部搜索（传递缓存以避免重复计算）
    # 使用迭代并行局部搜索获得更好的优化效果
    optimized_seed = local_search_iterative(
        seed, g, p, k, neighbors, max_hop, local_search_cache,
        max_iterations=5, n_jobs=8  # 使用8个线程进行并行计算
    )
    optimized_seed_set = set(optimized_seed)

    # 计算局部搜索后的PRE适应度（使用缓存）
    optimized_fitness = cached_PRE_for_local_search(optimized_seed_set)
    print(f"局部搜索后PRE适应度: {optimized_fitness:.6f}")
    print(f"局部搜索后种子集合: {sorted(optimized_seed)}")

    # 比较优化效果
    fitness_improvement = optimized_fitness - original_fitness
    print(f"PRE适应度提升: {fitness_improvement:.6f} ({fitness_improvement/original_fitness*100:.2f}%)")

    # 输出缓存统计信息
    print(f"局部搜索缓存大小: {len(local_search_cache)} 个不同的种子集合")
    print(f"缓存效果: 避免了重复计算，提升了局部搜索效率")

    # 如果局部搜索找到了更好的解，则使用优化后的种子集合
    if optimized_fitness > original_fitness:
        print("*** 局部搜索找到了更好的解，使用优化后的种子集合 ***")
        seed = optimized_seed_set
        # 更新详细结果中的最优解信息
        detailed_results['final_best_fitness'] = float(optimized_fitness)
        detailed_results['final_best_solution'] = sorted(list(optimized_seed_set))
        detailed_results['local_search_applied'] = True
        detailed_results['local_search_improvement'] = float(fitness_improvement)
    else:
        print("局部搜索未找到更好的解，保持原解")
        detailed_results['local_search_applied'] = False
        detailed_results['local_search_improvement'] = 0.0

    # 使用蒙特卡洛IC模型进行最终影响力评估
    print("\n=== 使用蒙特卡洛IC模型进行最终影响力评估 ===")
    mc = 1000
    influence = IC(g, seed, p, mc)

    end_time = time.time()
    runtime = end_time - start_time

    # 更新详细结果中的运行时间和IC评估
    detailed_results['total_runtime'] = runtime
    detailed_results['ic_evaluation'] = {
        'monte_carlo_runs': mc,
        'estimated_influence': float(influence)
    }

    # 输出结果
    print(f"\n=== 最终结果 ===")
    print(f"网络: {network_name}")
    print(f"种子集合大小: {k}")
    print(f"全局最优适应度: {detailed_results['final_best_fitness']:.6f}")
    print(f"IC模型估计影响力: {influence:.2f}")
    print(f"运行时间: {runtime:.2f}秒")
    print(f"最优种子集合: {sorted(seed)}")

    # 创建结果目录（按网络名称分文件夹）
    result_dir = f"result/{network_name}"
    os.makedirs(result_dir, exist_ok=True)
    # 调用绘图函数生成可视化结果
    print("正在生成可视化结果...")

    # 生成主要的进化曲线图（最优个体适应度变化和标准差变化）
    evolution_plot = plot_evolution_results(best_history, detailed_results, network_name, k, result_dir)
    print(f"已保存进化曲线到: {evolution_plot}")

    # 生成算法性能总结图
    summary_plot = create_summary_plot(detailed_results, network_name, k, result_dir)
    print(f"已保存性能总结图到: {summary_plot}")

    # 保存完整的详细结果（TXT格式）- 现在所有信息都已完整
    result_file = os.path.join(result_dir, f"detailed_results_k{k}.txt")
    _save_detailed_results_txt(detailed_results, result_file)
    print(f"已保存详细结果到: {result_file}")

    print(f"所有结果已保存到目录: {result_dir}")

    # plt.show()


if __name__ == "__main__":
    main()