
"""
局部搜索算法模块
提供高效的局部搜索优化功能，支持缓存机制和可选的线程并行
"""

from typing import Set, List, Dict, Optional, Union
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from base_fun import PRE

def local_search(xi: Union[Set[int], List[int]], G, p: float, k: int,
                neighbors: Optional[Dict[int, List[int]]] = None,
                max_hop: int = 5,
                fitness_cache: Optional[Dict] = None,
                use_parallel: bool = False,
                n_jobs: int = 4) -> List[int]:
    """
    局部搜索算法，优化种子节点集合（带缓存机制）

    Args:
        xi: 当前种子集合
        G: NetworkX 图对象
        p: 传播概率
        k: 种子集合大小
        neighbors: 邻居字典，格式为 {node: [neighbors]}
        max_hop: PRE递推轮数
        fitness_cache: 适应度缓存字典，用于避免重复计算
        use_parallel: 是否使用并行计算（仅在候选解数量较多时推荐）
        n_jobs: 并行线程数

    Returns:
        list: 优化后的种子集合
    """
    # 如果没有提供neighbors字典，则生成一个
    if neighbors is None:
        neighbors = {v: list(G.neighbors(v)) for v in G.nodes()}

    # 确保xi是集合类型
    if not isinstance(xi, set):
        xi = set(xi)

    # 初始化缓存（如果没有提供）
    if fitness_cache is None:
        fitness_cache = {}

    # 线程锁，用于保护缓存的并发访问
    cache_lock = threading.Lock()

    def cached_PRE(seed_set: Set[int]) -> float:
        """带缓存的PRE计算函数（线程安全）"""
        cache_key = tuple(sorted(seed_set))

        # 先检查缓存（读操作，使用锁保护）
        with cache_lock:
            if cache_key in fitness_cache:
                return fitness_cache[cache_key]

        # 计算PRE值
        fitness_value = PRE(G, seed_set, p, neighbors, max_hop)

        # 更新缓存（写操作，使用锁保护）
        with cache_lock:
            fitness_cache[cache_key] = fitness_value

        return fitness_value

    # 缓存当前种子集合的影响力值
    xi_fitness = cached_PRE(xi)

    # -------------------
    # 1. 生成候选解列表
    # -------------------
    candidates = []
    for x_ij in list(xi):
        for neighbor in neighbors[x_ij]:
            if neighbor not in xi:
                xi_new = xi.copy()
                xi_new.remove(x_ij)
                xi_new.add(neighbor)
                candidates.append((x_ij, neighbor, xi_new))

    if not candidates:
        return list(xi)

    # -------------------
    # 2. 计算候选解的适应度（串行或并行）
    # -------------------
    results = {}

    # 根据候选解数量和用户设置决定是否使用并行
    # 只有在候选解数量较多且用户明确要求时才使用并行
    if use_parallel and len(candidates) > 20 and n_jobs > 1:
        # 使用线程池并行计算（共享内存，缓存有效）
        with ThreadPoolExecutor(max_workers=n_jobs) as executor:
            future_to_cand = {executor.submit(cached_PRE, cand[2]): cand for cand in candidates}
            for future in as_completed(future_to_cand):
                cand = future_to_cand[future]
                try:
                    fitness = future.result()
                    results[cand] = fitness
                except Exception as e:
                    print(f"Error in parallel PRE calculation: {e}")
                    # 发生错误时使用串行计算作为备选
                    results[cand] = cached_PRE(cand[2])
    else:
        # 串行计算（推荐方式，缓存效果最佳）
        for cand in candidates:
            results[cand] = cached_PRE(cand[2])

    # -------------------
    # 3. 找到提升最大的候选解
    # -------------------
    best_cand, best_fitness = None, xi_fitness
    for cand, fitness in results.items():
        if fitness > best_fitness:
            best_cand, best_fitness = cand, fitness

    if best_cand is not None:
        xi = best_cand[2]  # 更新为最优解

    return list(xi)


def local_search_iterative(xi: Union[Set[int], List[int]], G, p: float, k: int,
                          neighbors: Optional[Dict[int, List[int]]] = None,
                          max_hop: int = 5,
                          fitness_cache: Optional[Dict] = None,
                          max_iterations: int = 10) -> List[int]:
    """
    迭代局部搜索算法，重复执行局部搜索直到无法改进

    Args:
        xi: 当前种子集合
        G: NetworkX 图对象
        p: 传播概率
        k: 种子集合大小
        neighbors: 邻居字典
        max_hop: PRE递推轮数
        fitness_cache: 适应度缓存字典
        max_iterations: 最大迭代次数

    Returns:
        list: 优化后的种子集合
    """
    current_solution = list(xi) if isinstance(xi, set) else xi.copy()

    for iteration in range(max_iterations):
        # 执行一轮局部搜索
        improved_solution = local_search(
            current_solution, G, p, k, neighbors, max_hop, fitness_cache,
            use_parallel=False  # 迭代局部搜索中使用串行版本更高效
        )

        # 如果没有改进，停止迭代
        if set(improved_solution) == set(current_solution):
            break

        current_solution = improved_solution

    return current_solution


def local_search_best_improvement(xi: Union[Set[int], List[int]], G, p: float, k: int,
                                neighbors: Optional[Dict[int, List[int]]] = None,
                                max_hop: int = 5,
                                fitness_cache: Optional[Dict] = None) -> List[int]:
    """
    最佳改进局部搜索：评估所有邻居解，选择最佳的进行替换
    相比于first_improvement策略，这种方法更彻底但计算量更大

    Args:
        xi: 当前种子集合
        G: NetworkX 图对象
        p: 传播概率
        k: 种子集合大小
        neighbors: 邻居字典
        max_hop: PRE递推轮数
        fitness_cache: 适应度缓存字典

    Returns:
        list: 优化后的种子集合
    """
    # 如果没有提供neighbors字典，则生成一个
    if neighbors is None:
        neighbors = {v: list(G.neighbors(v)) for v in G.nodes()}

    # 确保xi是集合类型
    if not isinstance(xi, set):
        xi = set(xi)

    # 初始化缓存（如果没有提供）
    if fitness_cache is None:
        fitness_cache = {}

    def cached_PRE(seed_set: Set[int]) -> float:
        """带缓存的PRE计算函数"""
        cache_key = tuple(sorted(seed_set))
        if cache_key not in fitness_cache:
            fitness_cache[cache_key] = PRE(G, seed_set, p, neighbors, max_hop)
        return fitness_cache[cache_key]

    # 缓存当前种子集合的影响力值
    xi_fitness = cached_PRE(xi)

    # 生成所有可能的邻居解
    all_candidates = []
    for x_ij in list(xi):
        for neighbor in neighbors[x_ij]:
            if neighbor not in xi:
                xi_new = xi.copy()
                xi_new.remove(x_ij)
                xi_new.add(neighbor)
                all_candidates.append((x_ij, neighbor, xi_new))

    if not all_candidates:
        return list(xi)

    # 评估所有候选解，找到最佳的
    best_candidate = None
    best_fitness = xi_fitness

    for candidate in all_candidates:
        candidate_fitness = cached_PRE(candidate[2])
        if candidate_fitness > best_fitness:
            best_candidate = candidate
            best_fitness = candidate_fitness

    # 如果找到更好的解，返回它
    if best_candidate is not None:
        return list(best_candidate[2])
    else:
        return list(xi)
