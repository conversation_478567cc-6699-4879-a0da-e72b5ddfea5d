#!/usr/bin/env python3
"""
测试局部搜索并行实现的正确性和效率
"""

import time
import networkx as nx
from base_fun import gen_graph, PRE
from local_search import local_search, local_search_iterative, local_search_best_improvement

def test_local_search_parallel():
    """测试并行局部搜索的正确性和效率"""
    
    # 创建一个小型测试网络
    print("=== 创建测试网络 ===")
    G = nx.erdos_renyi_graph(100, 0.1, seed=42)
    print(f"测试网络: {G.number_of_nodes()}个节点, {G.number_of_edges()}条边")
    
    # 参数设置
    p = 0.05
    k = 10
    max_hop = 3
    
    # 预计算邻接列表
    neighbors = {v: list(G.neighbors(v)) for v in G.nodes()}
    
    # 随机初始化种子集合
    import random
    random.seed(42)
    initial_seed = set(random.sample(list(G.nodes()), k))
    print(f"初始种子集合: {sorted(initial_seed)}")
    
    # 计算初始适应度
    initial_fitness = PRE(G, initial_seed, p, neighbors, max_hop)
    print(f"初始PRE适应度: {initial_fitness:.6f}")
    
    print("\n=== 测试不同并行度的局部搜索 ===")
    
    # 测试不同的并行线程数
    thread_counts = [1, 2, 4, 8]
    results = {}
    
    for n_jobs in thread_counts:
        print(f"\n--- 测试 {n_jobs} 个线程 ---")
        
        # 创建独立的缓存
        cache = {}
        
        # 测试基本局部搜索
        start_time = time.time()
        optimized_seed = local_search(
            initial_seed, G, p, k, neighbors, max_hop, cache, n_jobs
        )
        end_time = time.time()
        
        optimized_fitness = PRE(G, set(optimized_seed), p, neighbors, max_hop)
        improvement = optimized_fitness - initial_fitness
        runtime = end_time - start_time
        
        results[n_jobs] = {
            'optimized_seed': optimized_seed,
            'optimized_fitness': optimized_fitness,
            'improvement': improvement,
            'runtime': runtime,
            'cache_size': len(cache)
        }
        
        print(f"优化后种子集合: {sorted(optimized_seed)}")
        print(f"优化后PRE适应度: {optimized_fitness:.6f}")
        print(f"适应度提升: {improvement:.6f} ({improvement/initial_fitness*100:.2f}%)")
        print(f"运行时间: {runtime:.4f}秒")
        print(f"缓存大小: {len(cache)} 个种子集合")
    
    print("\n=== 并行效率分析（直接并行，无自适应） ===")
    baseline_time = results[1]['runtime']
    for n_jobs in thread_counts:
        result = results[n_jobs]
        speedup = baseline_time / result['runtime'] if result['runtime'] > 0 else float('inf')
        efficiency = speedup / n_jobs * 100
        print(f"{n_jobs:2d} 线程: 运行时间={result['runtime']:.4f}s, "
              f"加速比={speedup:.2f}x, 效率={efficiency:.1f}%")
    
    print("\n=== 测试迭代局部搜索 ===")
    
    # 测试迭代局部搜索
    cache = {}
    start_time = time.time()
    iterative_seed = local_search_iterative(
        initial_seed, G, p, k, neighbors, max_hop, cache, 
        max_iterations=3, n_jobs=4
    )
    end_time = time.time()
    
    iterative_fitness = PRE(G, set(iterative_seed), p, neighbors, max_hop)
    iterative_improvement = iterative_fitness - initial_fitness
    iterative_runtime = end_time - start_time
    
    print(f"迭代优化后种子集合: {sorted(iterative_seed)}")
    print(f"迭代优化后PRE适应度: {iterative_fitness:.6f}")
    print(f"迭代适应度提升: {iterative_improvement:.6f} ({iterative_improvement/initial_fitness*100:.2f}%)")
    print(f"迭代运行时间: {iterative_runtime:.4f}秒")
    print(f"迭代缓存大小: {len(cache)} 个种子集合")
    
    print("\n=== 测试最佳改进局部搜索 ===")
    
    # 测试最佳改进局部搜索
    cache = {}
    start_time = time.time()
    best_improvement_seed = local_search_best_improvement(
        initial_seed, G, p, k, neighbors, max_hop, cache, n_jobs=4
    )
    end_time = time.time()
    
    best_improvement_fitness = PRE(G, set(best_improvement_seed), p, neighbors, max_hop)
    best_improvement_improvement = best_improvement_fitness - initial_fitness
    best_improvement_runtime = end_time - start_time
    
    print(f"最佳改进优化后种子集合: {sorted(best_improvement_seed)}")
    print(f"最佳改进优化后PRE适应度: {best_improvement_fitness:.6f}")
    print(f"最佳改进适应度提升: {best_improvement_improvement:.6f} ({best_improvement_improvement/initial_fitness*100:.2f}%)")
    print(f"最佳改进运行时间: {best_improvement_runtime:.4f}秒")
    print(f"最佳改进缓存大小: {len(cache)} 个种子集合")
    
    print("\n=== 总结 ===")
    print(f"初始适应度: {initial_fitness:.6f}")
    print(f"基本局部搜索最佳提升: {max(r['improvement'] for r in results.values()):.6f}")
    print(f"迭代局部搜索提升: {iterative_improvement:.6f}")
    print(f"最佳改进局部搜索提升: {best_improvement_improvement:.6f}")
    
    # 验证所有结果的一致性（相同输入应该产生相同或更好的结果）
    all_improvements = [r['improvement'] for r in results.values()]
    if len(set(all_improvements)) == 1:
        print("✓ 所有并行度产生了一致的结果")
    else:
        print("⚠ 不同并行度产生了不同的结果，这可能是正常的（由于线程调度的随机性）")
        print(f"  改进范围: {min(all_improvements):.6f} - {max(all_improvements):.6f}")

if __name__ == "__main__":
    test_local_search_parallel()
